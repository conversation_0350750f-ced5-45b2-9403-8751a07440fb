import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Upload,
  Modal,
  Form,
  Input,
  Select,
  message,
  Breadcrumb,
  Popconfirm,
  Progress,
  Tag,
} from 'antd';
import {
  UploadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  FolderOutlined,
  FileOutlined,
  EditOutlined,
  PlusOutlined,
  HomeOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { fileApi, serverApi } from '../../services/api';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;
const { TextArea } = Input;

interface FileItem {
  name: string;
  type: 'file' | 'directory';
  size: number;
  modified: string;
  permissions: string;
  owner: string;
  path: string;
}

interface Server {
  id: number;
  name: string;
  host: string;
}

const FileManagement: React.FC = () => {
  const [servers, setServers] = useState<Server[]>([]);
  const [selectedServer, setSelectedServer] = useState<number | null>(null);
  const [files, setFiles] = useState<FileItem[]>([]);
  const [currentPath, setCurrentPath] = useState('/');
  const [loading, setLoading] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const [fileContent, setFileContent] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [form] = Form.useForm();

  // 加载服务器列表
  const loadServers = async () => {
    try {
      const response = await serverApi.getServers();
      // API响应拦截器已经返回了data，所以response直接就是数据
      const data = Array.isArray(response) ? response : [];
      setServers(data);
      if (data.length > 0 && !selectedServer) {
        setSelectedServer(data[0].id);
      }
    } catch (error) {
      console.error('加载服务器列表失败:', error);
    }
  };

  // 加载文件列表
  const loadFiles = async (path: string = currentPath) => {
    if (!selectedServer) return;

    try {
      setLoading(true);
      const response = await fileApi.listFiles(selectedServer, path);
      setFiles(response.data || []);
      setCurrentPath(path);
    } catch (error) {
      console.error('加载文件列表失败:', error);
      message.error('加载文件列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 文件操作
  const handleDownload = async (file: FileItem) => {
    try {
      const response = await fileApi.downloadFile(selectedServer!, file.path);
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', file.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      message.success('文件下载成功');
    } catch (error) {
      message.error('文件下载失败');
    }
  };

  const handleDelete = async (file: FileItem) => {
    try {
      await fileApi.deleteFile(selectedServer!, file.path);
      await loadFiles();
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleEdit = async (file: FileItem) => {
    try {
      const response = await fileApi.getFileContent(selectedServer!, file.path);
      setFileContent(response.data);
      setSelectedFile(file);
      setEditModalVisible(true);
    } catch (error) {
      message.error('读取文件内容失败');
    }
  };

  const handleSaveFile = async () => {
    try {
      await fileApi.saveFileContent(selectedServer!, selectedFile!.path, fileContent);
      setEditModalVisible(false);
      message.success('文件保存成功');
    } catch (error) {
      message.error('文件保存失败');
    }
  };

  const handleCreateDirectory = async () => {
    try {
      const values = await form.validateFields();
      const newPath = `${currentPath}/${values.name}`.replace('//', '/');
      await fileApi.createDirectory(selectedServer!, newPath);
      await loadFiles();
      setCreateModalVisible(false);
      form.resetFields();
      message.success('目录创建成功');
    } catch (error) {
      message.error('目录创建失败');
    }
  };

  const handleNavigate = (path: string) => {
    loadFiles(path);
  };

  const handleUpload = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('remote_path', currentPath);

    try {
      setUploadProgress(0);
      await fileApi.uploadFile(selectedServer!, formData);
      await loadFiles();
      setUploadModalVisible(false);
      message.success('文件上传成功');
    } catch (error) {
      message.error('文件上传失败');
    }
  };

  useEffect(() => {
    loadServers();
  }, []);

  useEffect(() => {
    if (selectedServer) {
      loadFiles('/');
    }
  }, [selectedServer]);

  // 生成面包屑导航
  const getBreadcrumbItems = () => {
    const paths = currentPath.split('/').filter(Boolean);
    const items = [
      {
        title: <HomeOutlined onClick={() => handleNavigate('/')} style={{ cursor: 'pointer' }} />,
      },
    ];

    let fullPath = '';
    paths.forEach((path, index) => {
      fullPath += `/${path}`;
      items.push({
        title: (
          <span
            onClick={() => handleNavigate(fullPath)}
            style={{ cursor: 'pointer' }}
          >
            {path}
          </span>
        ),
      });
    });

    return items;
  };

  const columns: ColumnsType<FileItem> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {record.type === 'directory' ? <FolderOutlined /> : <FileOutlined />}
          <span
            style={{ cursor: record.type === 'directory' ? 'pointer' : 'default' }}
            onClick={() => record.type === 'directory' && handleNavigate(record.path)}
          >
            {text}
          </span>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={type === 'directory' ? 'blue' : 'green'}>
          {type === 'directory' ? '目录' : '文件'}
        </Tag>
      ),
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      render: (size) => {
        if (size < 1024) return `${size} B`;
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
        return `${(size / (1024 * 1024)).toFixed(1)} MB`;
      },
    },
    {
      title: '修改时间',
      dataIndex: 'modified',
      key: 'modified',
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      key: 'permissions',
    },
    {
      title: '所有者',
      dataIndex: 'owner',
      key: 'owner',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          {record.type === 'file' && (
            <>
              <Button
                type="link"
                icon={<DownloadOutlined />}
                onClick={() => handleDownload(record)}
              >
                下载
              </Button>
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              >
                编辑
              </Button>
            </>
          )}
          <Popconfirm
            title={`确定要删除${record.type === 'directory' ? '目录' : '文件'} "${record.name}" 吗？`}
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="文件管理"
        extra={
          <Space>
            <Select
              style={{ width: 200 }}
              placeholder="选择服务器"
              value={selectedServer}
              onChange={setSelectedServer}
            >
              {servers.map(server => (
                <Option key={server.id} value={server.id}>
                  {server.name} ({server.host})
                </Option>
              ))}
            </Select>
          </Space>
        }
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Breadcrumb items={getBreadcrumbItems()} />
          
          <Space>
            <Button icon={<ReloadOutlined />} onClick={() => loadFiles()}>
              刷新
            </Button>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
              disabled={!selectedServer}
            >
              上传文件
            </Button>
            <Button
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
              disabled={!selectedServer}
            >
              新建目录
            </Button>
          </Space>

          <Table
            columns={columns}
            dataSource={files}
            rowKey="path"
            loading={loading}
            pagination={false}
          />
        </Space>
      </Card>

      {/* 上传文件模态框 */}
      <Modal
        title="上传文件"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          beforeUpload={(file) => {
            handleUpload(file);
            return false;
          }}
          showUploadList={false}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">支持单个文件上传</p>
        </Upload.Dragger>
        {uploadProgress > 0 && (
          <Progress percent={uploadProgress} style={{ marginTop: 16 }} />
        )}
      </Modal>

      {/* 新建目录模态框 */}
      <Modal
        title="新建目录"
        open={createModalVisible}
        onOk={handleCreateDirectory}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="目录名称"
            rules={[{ required: true, message: '请输入目录名称' }]}
          >
            <Input placeholder="请输入目录名称" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑文件模态框 */}
      <Modal
        title={`编辑文件: ${selectedFile?.name}`}
        open={editModalVisible}
        onOk={handleSaveFile}
        onCancel={() => setEditModalVisible(false)}
        width={800}
      >
        <TextArea
          value={fileContent}
          onChange={(e) => setFileContent(e.target.value)}
          rows={20}
          style={{ fontFamily: 'monospace' }}
        />
      </Modal>
    </div>
  );
};

export default FileManagement;
