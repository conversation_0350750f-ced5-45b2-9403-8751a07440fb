"""
Docker管理服务
"""
import json
import logging
import re
from typing import List, Dict, Optional, Any
from datetime import datetime

from ..models.server import Server
from ..models.docker import DockerI<PERSON>, DockerContainer, ContainerStats
from .ssh_manager import ssh_manager


logger = logging.getLogger(__name__)


class DockerManager:
    """Docker管理器"""
    
    def __init__(self):
        pass

    def _get_docker_command(self, server, base_command: str) -> str:
        """根据用户权限构建Docker命令"""
        # 如果不是root用户，添加sudo
        if getattr(server, 'username', 'root') != 'root':
            return f"sudo {base_command}"
        return base_command
    
    async def list_images(self, server: Server) -> List[Dict[str, Any]]:
        """列出Docker镜像"""
        try:
            base_command = "docker images --format 'table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}' --no-trunc"
            command = self._get_docker_command(server, base_command)
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code != 0:
                raise Exception(f"获取镜像列表失败: {stderr}")
            
            images = []
            lines = stdout.strip().split('\n')[1:]  # 跳过表头
            
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 5:
                        images.append({
                            'repository': parts[0],
                            'tag': parts[1],
                            'image_id': parts[2],
                            'created': parts[3],
                            'size': parts[4]
                        })
            
            return images
            
        except Exception as e:
            logger.error(f"列出Docker镜像失败 {server.host}: {e}")
            raise
    
    async def pull_image(self, server: Server, image_name: str) -> bool:
        """拉取Docker镜像"""
        try:
            command = f"docker pull {image_name}"
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code != 0:
                raise Exception(f"拉取镜像失败: {stderr}")
            
            logger.info(f"成功拉取镜像 {image_name} 到服务器 {server.host}")
            return True
            
        except Exception as e:
            logger.error(f"拉取Docker镜像失败 {server.host}: {e}")
            raise
    
    async def remove_image(self, server: Server, image_id: str, force: bool = False) -> bool:
        """删除Docker镜像"""
        try:
            force_flag = "-f" if force else ""
            command = f"docker rmi {force_flag} {image_id}"
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code != 0:
                raise Exception(f"删除镜像失败: {stderr}")
            
            logger.info(f"成功删除镜像 {image_id} 从服务器 {server.host}")
            return True
            
        except Exception as e:
            logger.error(f"删除Docker镜像失败 {server.host}: {e}")
            raise
    
    async def list_containers(self, server: Server, all_containers: bool = True) -> List[Dict[str, Any]]:
        """列出Docker容器"""
        try:
            all_flag = "-a" if all_containers else ""
            base_command = f"docker ps {all_flag} --format 'table {{{{.ID}}}}\t{{{{.Names}}}}\t{{{{.Image}}}}\t{{{{.Status}}}}\t{{{{.Ports}}}}\t{{{{.CreatedAt}}}}' --no-trunc"
            command = self._get_docker_command(server, base_command)
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code != 0:
                raise Exception(f"获取容器列表失败: {stderr}")
            
            containers = []
            lines = stdout.strip().split('\n')[1:]  # 跳过表头
            
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 6:
                        containers.append({
                            'container_id': parts[0],
                            'name': parts[1],
                            'image': parts[2],
                            'status': parts[3],
                            'ports': parts[4],
                            'created': parts[5]
                        })
            
            return containers
            
        except Exception as e:
            logger.error(f"列出Docker容器失败 {server.host}: {e}")
            raise
    
    async def create_container(self, server: Server, config: Dict[str, Any]) -> str:
        """创建Docker容器"""
        try:
            # 构建docker run命令
            command_parts = ["docker run -d"]
            
            # 容器名称
            if config.get('name'):
                command_parts.append(f"--name {config['name']}")
            
            # 端口映射
            if config.get('ports'):
                for port_mapping in config['ports']:
                    command_parts.append(f"-p {port_mapping}")
            
            # 环境变量
            if config.get('environment'):
                for env_var in config['environment']:
                    command_parts.append(f"-e {env_var}")
            
            # 挂载卷
            if config.get('volumes'):
                for volume in config['volumes']:
                    command_parts.append(f"-v {volume}")
            
            # 网络
            if config.get('network'):
                command_parts.append(f"--network {config['network']}")
            
            # 资源限制
            if config.get('cpu_limit'):
                command_parts.append(f"--cpus {config['cpu_limit']}")
            
            if config.get('memory_limit'):
                command_parts.append(f"--memory {config['memory_limit']}")
            
            # 重启策略
            if config.get('restart_policy'):
                command_parts.append(f"--restart {config['restart_policy']}")
            
            # 工作目录
            if config.get('working_dir'):
                command_parts.append(f"--workdir {config['working_dir']}")
            
            # 镜像名称
            command_parts.append(config['image'])
            
            # 启动命令
            if config.get('command'):
                command_parts.append(config['command'])
            
            command = " ".join(command_parts)
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code != 0:
                raise Exception(f"创建容器失败: {stderr}")
            
            container_id = stdout.strip()
            logger.info(f"成功创建容器 {container_id} 在服务器 {server.host}")
            return container_id
            
        except Exception as e:
            logger.error(f"创建Docker容器失败 {server.host}: {e}")
            raise
    
    async def start_container(self, server: Server, container_id: str) -> bool:
        """启动容器"""
        try:
            command = f"docker start {container_id}"
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code != 0:
                raise Exception(f"启动容器失败: {stderr}")
            
            logger.info(f"成功启动容器 {container_id} 在服务器 {server.host}")
            return True
            
        except Exception as e:
            logger.error(f"启动Docker容器失败 {server.host}: {e}")
            raise
    
    async def stop_container(self, server: Server, container_id: str, timeout: int = 10) -> bool:
        """停止容器"""
        try:
            command = f"docker stop -t {timeout} {container_id}"
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code != 0:
                raise Exception(f"停止容器失败: {stderr}")
            
            logger.info(f"成功停止容器 {container_id} 在服务器 {server.host}")
            return True
            
        except Exception as e:
            logger.error(f"停止Docker容器失败 {server.host}: {e}")
            raise
    
    async def restart_container(self, server: Server, container_id: str) -> bool:
        """重启容器"""
        try:
            command = f"docker restart {container_id}"
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code != 0:
                raise Exception(f"重启容器失败: {stderr}")
            
            logger.info(f"成功重启容器 {container_id} 在服务器 {server.host}")
            return True
            
        except Exception as e:
            logger.error(f"重启Docker容器失败 {server.host}: {e}")
            raise
    
    async def remove_container(self, server: Server, container_id: str, force: bool = False) -> bool:
        """删除容器"""
        try:
            force_flag = "-f" if force else ""
            command = f"docker rm {force_flag} {container_id}"
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code != 0:
                raise Exception(f"删除容器失败: {stderr}")
            
            logger.info(f"成功删除容器 {container_id} 从服务器 {server.host}")
            return True
            
        except Exception as e:
            logger.error(f"删除Docker容器失败 {server.host}: {e}")
            raise
    
    async def get_container_logs(self, server: Server, container_id: str, lines: int = 100) -> str:
        """获取容器日志"""
        try:
            base_command = f"docker logs --tail {lines} {container_id}"
            command = self._get_docker_command(server, base_command)
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            # 日志可能在stderr中
            logs = stdout + stderr
            return logs
            
        except Exception as e:
            logger.error(f"获取容器日志失败 {server.host}: {e}")
            raise
    
    async def get_container_stats(self, server: Server, container_id: str) -> Dict[str, Any]:
        """获取容器资源统计"""
        try:
            command = f"docker stats --no-stream --format 'table {{{{.Container}}}}\t{{{{.CPUPerc}}}}\t{{{{.MemUsage}}}}\t{{{{.MemPerc}}}}\t{{{{.NetIO}}}}\t{{{{.BlockIO}}}}' {container_id}"
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code != 0:
                raise Exception(f"获取容器统计失败: {stderr}")
            
            lines = stdout.strip().split('\n')
            if len(lines) < 2:
                return {}
            
            data_line = lines[1]
            parts = data_line.split('\t')
            
            if len(parts) >= 6:
                # 解析内存使用情况
                mem_usage_parts = parts[2].split(' / ')
                mem_usage = mem_usage_parts[0] if len(mem_usage_parts) > 0 else "0B"
                mem_limit = mem_usage_parts[1] if len(mem_usage_parts) > 1 else "0B"
                
                return {
                    'container_id': parts[0],
                    'cpu_usage_percent': float(parts[1].replace('%', '')),
                    'memory_usage': mem_usage,
                    'memory_limit': mem_limit,
                    'memory_usage_percent': float(parts[3].replace('%', '')),
                    'network_io': parts[4],
                    'block_io': parts[5]
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"获取容器统计失败 {server.host}: {e}")
            raise
    
    async def get_gpu_stats(self, server: Server) -> List[Dict[str, Any]]:
        """获取GPU统计信息"""
        try:
            # 尝试使用hy-smi命令
            command = "/opt/hyhal/bin/hy-smi"
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code == 0:
                return self._parse_hy_smi_output(stdout)
            
            # 如果hy-smi不可用，尝试nvidia-smi
            command = "nvidia-smi --query-gpu=index,name,utilization.gpu,memory.used,memory.total --format=csv,noheader,nounits"
            exit_code, stdout, stderr = await ssh_manager.execute_command(server, command)
            
            if exit_code == 0:
                return self._parse_nvidia_smi_output(stdout)
            
            return []
            
        except Exception as e:
            logger.error(f"获取GPU统计失败 {server.host}: {e}")
            return []
    
    def _parse_hy_smi_output(self, output: str) -> List[Dict[str, Any]]:
        """解析hy-smi输出"""
        gpu_stats = []
        lines = output.strip().split('\n')
        
        for line in lines:
            if 'GPU' in line and '%' in line:
                # 解析hy-smi输出格式
                # 这里需要根据实际的hy-smi输出格式进行调整
                parts = line.split()
                if len(parts) >= 4:
                    gpu_stats.append({
                        'gpu_id': len(gpu_stats),
                        'name': 'Unknown',
                        'utilization': 0,
                        'memory_used': 0,
                        'memory_total': 0
                    })
        
        return gpu_stats
    
    def _parse_nvidia_smi_output(self, output: str) -> List[Dict[str, Any]]:
        """解析nvidia-smi输出"""
        gpu_stats = []
        lines = output.strip().split('\n')
        
        for line in lines:
            if line.strip():
                parts = [p.strip() for p in line.split(',')]
                if len(parts) >= 5:
                    gpu_stats.append({
                        'gpu_id': int(parts[0]),
                        'name': parts[1],
                        'utilization': int(parts[2]),
                        'memory_used': int(parts[3]),
                        'memory_total': int(parts[4])
                    })
        
        return gpu_stats


# 全局Docker管理器实例
docker_manager = DockerManager()
