"""
SSH隧道管理服务
"""
import asyncio
import logging
import time
import uuid
from typing import Dict, Optional, Tuple, Any
from concurrent.futures import ThreadPoolExecutor
import paramiko
from paramiko import SSHClient, AutoAddPolicy
from scp import SCPClient
import threading

from ..core.config import settings
from ..models.server import Server, SSHConnection
from ..core.security import decrypt_password


logger = logging.getLogger(__name__)


class SSHConnectionPool:
    """SSH连接池"""
    
    def __init__(self, max_connections: int = 100):
        self.max_connections = max_connections
        self.connections: Dict[str, paramiko.SSHClient] = {}
        self.connection_info: Dict[str, dict] = {}
        self.lock = threading.RLock()
        self.executor = ThreadPoolExecutor(max_workers=20)
        
    def get_connection_id(self, server: Server) -> str:
        """生成连接ID"""
        return f"{server.host}:{server.port}:{server.username}"
    
    async def get_connection(self, server: Server) -> Optional[paramiko.SSHClient]:
        """获取SSH连接"""
        connection_id = self.get_connection_id(server)
        
        with self.lock:
            # 检查现有连接
            if connection_id in self.connections:
                client = self.connections[connection_id]
                if self._is_connection_alive(client):
                    logger.info(f"复用SSH连接: {connection_id}")
                    return client
                else:
                    # 清理死连接
                    self._remove_connection(connection_id)
            
            # 创建新连接
            if len(self.connections) >= self.max_connections:
                logger.warning("SSH连接池已满，清理最旧的连接")
                self._cleanup_oldest_connection()
            
            client = await self._create_connection(server)
            if client:
                self.connections[connection_id] = client
                self.connection_info[connection_id] = {
                    "server": server,
                    "created_at": time.time(),
                    "last_used": time.time()
                }
                logger.info(f"创建新SSH连接: {connection_id}")
            
            return client
    
    async def _create_connection(self, server: Server) -> Optional[paramiko.SSHClient]:
        """创建SSH连接"""
        try:
            client = paramiko.SSHClient()
            client.set_missing_host_key_policy(AutoAddPolicy())
            
            # 配置连接参数
            connect_kwargs = {
                "hostname": server.host,
                "port": server.port,
                "username": server.username,
                "timeout": settings.SSH_TIMEOUT,
                "allow_agent": False,
                "look_for_keys": False
            }
            
            # 认证方式
            if server.auth_type == "password":
                # 解密密码
                decrypted_password = decrypt_password(server.password) if server.password else ""
                connect_kwargs["password"] = decrypted_password
            elif server.auth_type == "key":
                if server.private_key:
                    # 从字符串加载私钥
                    from io import StringIO
                    key_file = StringIO(server.private_key)
                    connect_kwargs["pkey"] = paramiko.RSAKey.from_private_key(key_file)
                elif server.private_key_path:
                    connect_kwargs["key_filename"] = server.private_key_path
            
            # 跳板机连接
            if server.jump_host:
                jump_client = paramiko.SSHClient()
                jump_client.set_missing_host_key_policy(AutoAddPolicy())
                
                jump_kwargs = {
                    "hostname": server.jump_host,
                    "port": server.jump_port or 22,
                    "username": server.jump_username,
                    "timeout": settings.SSH_TIMEOUT
                }
                
                if server.jump_password:
                    # 解密跳板机密码
                    decrypted_jump_password = decrypt_password(server.jump_password)
                    jump_kwargs["password"] = decrypted_jump_password
                elif server.jump_private_key:
                    from io import StringIO
                    key_file = StringIO(server.jump_private_key)
                    jump_kwargs["pkey"] = paramiko.RSAKey.from_private_key(key_file)
                
                # 连接跳板机
                await asyncio.get_event_loop().run_in_executor(
                    self.executor, jump_client.connect, **jump_kwargs
                )
                
                # 通过跳板机连接目标服务器
                jump_transport = jump_client.get_transport()
                dest_addr = (server.host, server.port)
                local_addr = (server.jump_host, server.jump_port or 22)
                channel = jump_transport.open_channel("direct-tcpip", dest_addr, local_addr)
                
                connect_kwargs["sock"] = channel
                del connect_kwargs["hostname"]
                del connect_kwargs["port"]
                
                await asyncio.get_event_loop().run_in_executor(
                    self.executor, client.connect, **connect_kwargs
                )
            else:
                # 直接连接
                await asyncio.get_event_loop().run_in_executor(
                    self.executor, client.connect, **connect_kwargs
                )
            
            # 启用keepalive
            transport = client.get_transport()
            if transport:
                transport.set_keepalive(settings.SSH_KEEPALIVE_INTERVAL)
            
            return client
            
        except Exception as e:
            logger.error(f"创建SSH连接失败 {server.host}:{server.port}: {e}")
            if 'client' in locals():
                client.close()
            return None
    
    def _is_connection_alive(self, client: paramiko.SSHClient) -> bool:
        """检查连接是否存活"""
        try:
            transport = client.get_transport()
            if not transport or not transport.is_active():
                return False
            
            # 发送心跳
            transport.send_ignore()
            return True
        except Exception:
            return False
    
    def _remove_connection(self, connection_id: str):
        """移除连接"""
        if connection_id in self.connections:
            try:
                self.connections[connection_id].close()
            except Exception:
                pass
            del self.connections[connection_id]
            
        if connection_id in self.connection_info:
            del self.connection_info[connection_id]
    
    def _cleanup_oldest_connection(self):
        """清理最旧的连接"""
        if not self.connection_info:
            return
        
        oldest_id = min(
            self.connection_info.keys(),
            key=lambda x: self.connection_info[x]["last_used"]
        )
        self._remove_connection(oldest_id)
    
    def close_all(self):
        """关闭所有连接"""
        with self.lock:
            for client in self.connections.values():
                try:
                    client.close()
                except Exception:
                    pass
            self.connections.clear()
            self.connection_info.clear()


class SSHManager:
    """SSH管理器"""
    
    def __init__(self):
        self.pool = SSHConnectionPool(settings.SSH_MAX_CONNECTIONS)
        self.heartbeat_task = None
        self.running = False
    
    async def start(self):
        """启动SSH管理器"""
        self.running = True
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        logger.info("SSH管理器已启动")
    
    async def stop(self):
        """停止SSH管理器"""
        self.running = False
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        self.pool.close_all()
        logger.info("SSH管理器已停止")
    
    async def execute_command(self, server: Server, command: str) -> Tuple[int, str, str]:
        """执行远程命令"""
        client = await self.pool.get_connection(server)
        if not client:
            raise Exception(f"无法连接到服务器 {server.host}")

        try:
            # 检查是否是sudo命令
            if command.strip().startswith('sudo '):
                return await self._execute_sudo_command(client, server, command)
            else:
                stdin, stdout, stderr = client.exec_command(command, timeout=300)
                exit_code = stdout.channel.recv_exit_status()
                stdout_data = stdout.read().decode('utf-8')
                stderr_data = stderr.read().decode('utf-8')

                return exit_code, stdout_data, stderr_data

        except Exception as e:
            logger.error(f"执行命令失败 {server.host}: {command}, 错误: {e}")
            raise

    async def _execute_sudo_command(self, client, server: Server, command: str) -> Tuple[int, str, str]:
        """执行sudo命令，处理密码提示"""
        try:
            # 使用-S选项让sudo从stdin读取密码
            sudo_command = command.replace('sudo ', 'sudo -S ', 1)
            stdin, stdout, stderr = client.exec_command(sudo_command, timeout=300, get_pty=True)

            # 发送密码
            if hasattr(server, 'password') and server.password:
                from ..core.security import decrypt_password
                password = decrypt_password(server.password)
                stdin.write(password + '\n')
                stdin.flush()

            # 等待命令完成
            exit_code = stdout.channel.recv_exit_status()
            stdout_data = stdout.read().decode('utf-8')
            stderr_data = stderr.read().decode('utf-8')

            # 清理输出中的密码提示
            if stdout_data.startswith('[sudo]'):
                lines = stdout_data.split('\n')
                stdout_data = '\n'.join(lines[1:])

            return exit_code, stdout_data, stderr_data

        except Exception as e:
            logger.error(f"执行sudo命令失败 {server.host}: {command}, 错误: {e}")
            raise
    
    async def upload_file(self, server: Server, local_path: str, remote_path: str):
        """上传文件"""
        client = await self.pool.get_connection(server)
        if not client:
            raise Exception(f"无法连接到服务器 {server.host}")
        
        try:
            with SCPClient(client.get_transport()) as scp:
                scp.put(local_path, remote_path)
        except Exception as e:
            logger.error(f"上传文件失败 {server.host}: {local_path} -> {remote_path}, 错误: {e}")
            raise
    
    async def download_file(self, server: Server, remote_path: str, local_path: str):
        """下载文件"""
        client = await self.pool.get_connection(server)
        if not client:
            raise Exception(f"无法连接到服务器 {server.host}")
        
        try:
            with SCPClient(client.get_transport()) as scp:
                scp.get(remote_path, local_path)
        except Exception as e:
            logger.error(f"下载文件失败 {server.host}: {remote_path} -> {local_path}, 错误: {e}")
            raise
    
    async def _heartbeat_loop(self):
        """心跳检测循环"""
        while self.running:
            try:
                await asyncio.sleep(settings.SSH_KEEPALIVE_INTERVAL)
                await self._check_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳检测错误: {e}")
    
    async def _check_connections(self):
        """检查所有连接状态"""
        with self.pool.lock:
            dead_connections = []
            for connection_id, client in self.pool.connections.items():
                if not self.pool._is_connection_alive(client):
                    dead_connections.append(connection_id)
            
            for connection_id in dead_connections:
                logger.warning(f"检测到死连接，清理: {connection_id}")
                self.pool._remove_connection(connection_id)


# 全局SSH管理器实例
ssh_manager = SSHManager()
