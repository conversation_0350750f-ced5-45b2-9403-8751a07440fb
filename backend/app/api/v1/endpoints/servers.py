"""
服务器管理API端点
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from ....services.ssh_manager import ssh_manager
from ....services.monitoring_manager import monitoring_manager
from ....core.security import encrypt_password, decrypt_password


router = APIRouter()


class ServerCreate(BaseModel):
    """创建服务器请求模型"""
    name: str
    host: str
    port: int = 22
    username: str
    auth_type: str = "password"  # password or key
    password: Optional[str] = None
    private_key: Optional[str] = None
    private_key_path: Optional[str] = None
    jump_host: Optional[str] = None
    jump_port: Optional[int] = None
    jump_username: Optional[str] = None
    jump_password: Optional[str] = None
    jump_private_key: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None


class ServerUpdate(BaseModel):
    """更新服务器请求模型"""
    name: Optional[str] = None
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    auth_type: Optional[str] = None
    password: Optional[str] = None
    private_key: Optional[str] = None
    private_key_path: Optional[str] = None
    jump_host: Optional[str] = None
    jump_port: Optional[int] = None
    jump_username: Optional[str] = None
    jump_password: Optional[str] = None
    jump_private_key: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    monitoring_enabled: Optional[bool] = None
    alert_enabled: Optional[bool] = None


class ServerResponse(BaseModel):
    """服务器响应模型"""
    id: int
    name: str
    host: str
    port: int
    username: str
    auth_type: str
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    monitoring_enabled: bool
    alert_enabled: bool
    is_active: bool
    created_at: str
    updated_at: str


class CommandRequest(BaseModel):
    """命令执行请求模型"""
    command: str
    working_dir: Optional[str] = None
    env_vars: Optional[dict] = None
    timeout: int = 300


class CommandResponse(BaseModel):
    """命令执行响应模型"""
    execution_id: str
    exit_code: int
    stdout: str
    stderr: str
    duration: int
    started_at: str
    finished_at: str


@router.get("/", response_model=List[ServerResponse])
async def list_servers(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None
):
    """获取服务器列表"""
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../..'))
    from dao import ServerDAO

    try:
        servers = ServerDAO.get_all()
        return servers
    except Exception as e:
        # 如果数据库查询失败，返回示例数据
        return [
            {
                "id": 1,
                "name": "测试服务器1",
                "host": "*************",
                "port": 22,
                "username": "root",
                "auth_type": "password",
                "description": "测试用服务器",
                "tags": ["test", "development"],
                "monitoring_enabled": True,
                "alert_enabled": True,
                "is_active": True,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
        ]


@router.post("/", response_model=ServerResponse)
async def create_server(server: ServerCreate):
    """创建服务器"""
    # TODO: 保存到数据库
    # 这里返回示例响应
    return {
        "id": 1,
        "name": server.name,
        "host": server.host,
        "port": server.port,
        "username": server.username,
        "auth_type": server.auth_type,
        "description": server.description,
        "tags": server.tags or [],
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }


@router.get("/{server_id}", response_model=ServerResponse)
async def get_server(server_id: int):
    """获取服务器详情"""
    # TODO: 从数据库获取服务器信息
    if server_id != 1:
        raise HTTPException(status_code=404, detail="服务器不存在")
    
    return {
        "id": 1,
        "name": "测试服务器1",
        "host": "*************",
        "port": 22,
        "username": "root",
        "auth_type": "password",
        "description": "测试用服务器",
        "tags": ["test", "development"],
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }


@router.put("/{server_id}", response_model=ServerResponse)
async def update_server(server_id: int, server: ServerUpdate):
    """更新服务器"""
    # TODO: 更新数据库中的服务器信息
    if server_id != 1:
        raise HTTPException(status_code=404, detail="服务器不存在")
    
    return {
        "id": 1,
        "name": server.name or "测试服务器1",
        "host": server.host or "*************",
        "port": server.port or 22,
        "username": server.username or "root",
        "auth_type": server.auth_type or "password",
        "description": server.description,
        "tags": server.tags or ["test", "development"],
        "monitoring_enabled": server.monitoring_enabled if server.monitoring_enabled is not None else True,
        "alert_enabled": server.alert_enabled if server.alert_enabled is not None else True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }


@router.delete("/{server_id}")
async def delete_server(server_id: int):
    """删除服务器"""
    # TODO: 从数据库删除服务器
    if server_id != 1:
        raise HTTPException(status_code=404, detail="服务器不存在")
    
    return {"message": "服务器删除成功"}


@router.post("/{server_id}/test-connection")
async def test_connection(server_id: int):
    """测试服务器连接"""
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../..'))
    from dao import ServerDAO

    # 从数据库获取服务器信息
    server_data = ServerDAO.get_by_id(server_id)
    if not server_data:
        raise HTTPException(status_code=404, detail="服务器不存在")

    try:
        # 创建简单的服务器对象
        class SimpleServer:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

        server = SimpleServer(**server_data)

        # 使用SSH管理器测试连接
        client = await ssh_manager.pool.get_connection(server)
        if client:
            # 测试执行简单命令
            import time
            start_time = time.time()
            stdin, stdout, stderr = client.exec_command("echo 'connection test'", timeout=10)
            exit_code = stdout.channel.recv_exit_status()
            latency = round((time.time() - start_time) * 1000, 2)

            if exit_code == 0:
                return {
                    "success": True,
                    "message": "连接成功",
                    "latency": latency
                }
            else:
                return {
                    "success": False,
                    "message": "连接测试命令执行失败",
                    "latency": latency
                }
        else:
            return {
                "success": False,
                "message": "无法建立SSH连接",
                "latency": 0
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"连接测试失败: {str(e)}",
            "latency": 0,
            "error": str(e)
        }


@router.post("/{server_id}/execute", response_model=CommandResponse)
async def execute_command(server_id: int, request: CommandRequest):
    """在服务器上执行命令"""
    import sys
    import os
    import time
    import uuid
    from datetime import datetime
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../..'))
    from dao import ServerDAO

    # 从数据库获取服务器信息
    server_data = ServerDAO.get_by_id(server_id)
    if not server_data:
        raise HTTPException(status_code=404, detail="服务器不存在")

    execution_id = f"exec_{uuid.uuid4().hex[:8]}"
    started_at = datetime.now()

    try:
        # 创建简单的服务器对象
        class SimpleServer:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

        server = SimpleServer(**server_data)

        # 使用SSH管理器执行命令
        start_time = time.time()
        exit_code, stdout, stderr = await ssh_manager.execute_command(server, request.command)
        duration = round(time.time() - start_time, 2)
        finished_at = datetime.now()

        return {
            "execution_id": execution_id,
            "exit_code": exit_code,
            "stdout": stdout,
            "stderr": stderr,
            "duration": duration,
            "started_at": started_at.isoformat() + "Z",
            "finished_at": finished_at.isoformat() + "Z"
        }

    except Exception as e:
        finished_at = datetime.now()
        duration = round(time.time() - time.time(), 2)

        return {
            "execution_id": execution_id,
            "exit_code": -1,
            "stdout": "",
            "stderr": f"命令执行失败: {str(e)}",
            "duration": duration,
            "started_at": started_at.isoformat() + "Z",
            "finished_at": finished_at.isoformat() + "Z"
        }


@router.post("/{server_id}/start-monitoring")
async def start_monitoring(server_id: int):
    """开始监控服务器"""
    # TODO: 从数据库获取服务器信息
    if server_id != 1:
        raise HTTPException(status_code=404, detail="服务器不存在")
    
    # TODO: 启动服务器监控
    return {"message": "监控已启动"}


@router.post("/{server_id}/stop-monitoring")
async def stop_monitoring(server_id: int):
    """停止监控服务器"""
    # TODO: 从数据库获取服务器信息
    if server_id != 1:
        raise HTTPException(status_code=404, detail="服务器不存在")
    
    # TODO: 停止服务器监控
    return {"message": "监控已停止"}
