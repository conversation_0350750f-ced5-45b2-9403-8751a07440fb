"""
数据库连接和模型定义
"""
import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from app.core.security import encrypt_password, decrypt_password


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "device_management.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        return conn
    
    def init_database(self):
        """初始化数据库表"""
        conn = self.get_connection()
        try:
            # 创建服务器表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS servers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    host TEXT NOT NULL,
                    port INTEGER DEFAULT 22,
                    username TEXT NOT NULL,
                    auth_type TEXT DEFAULT 'password',
                    password TEXT,
                    private_key TEXT,
                    private_key_path TEXT,
                    jump_host TEXT,
                    jump_port INTEGER,
                    jump_username TEXT,
                    jump_password TEXT,
                    jump_private_key TEXT,
                    description TEXT,
                    tags TEXT,  -- JSON格式存储
                    monitoring_enabled BOOLEAN DEFAULT 1,
                    alert_enabled BOOLEAN DEFAULT 1,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建脚本表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS scripts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    content TEXT NOT NULL,
                    script_type TEXT DEFAULT 'shell',
                    category TEXT,
                    tags TEXT,  -- JSON格式存储
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建脚本执行记录表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS script_executions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    script_id INTEGER,
                    server_id INTEGER,
                    status TEXT,
                    output TEXT,
                    error TEXT,
                    started_at TIMESTAMP,
                    finished_at TIMESTAMP,
                    FOREIGN KEY (script_id) REFERENCES scripts (id),
                    FOREIGN KEY (server_id) REFERENCES servers (id)
                )
            """)
            
            # 创建监控数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS monitoring_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    server_id INTEGER,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    cpu_usage REAL,
                    memory_usage REAL,
                    disk_usage REAL,
                    network_in REAL,
                    network_out REAL,
                    gpu_usage REAL,
                    gpu_memory REAL,
                    FOREIGN KEY (server_id) REFERENCES servers (id)
                )
            """)
            
            # 创建告警表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    server_id INTEGER,
                    server_name TEXT,
                    type TEXT,
                    severity TEXT,
                    message TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    acknowledged_at TIMESTAMP,
                    resolved_at TIMESTAMP,
                    FOREIGN KEY (server_id) REFERENCES servers (id)
                )
            """)
            
            conn.commit()
            print("数据库初始化完成")
            
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def insert_sample_data(self):
        """插入示例数据（已禁用）"""
        # 不再插入测试数据
        pass


# 全局数据库管理器实例
db_manager = DatabaseManager()
