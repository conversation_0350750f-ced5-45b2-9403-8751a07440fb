#!/usr/bin/env python3
"""
添加测试服务器脚本
为系统添加一些测试服务器以便测试功能
"""
import json
from database import db_manager, encrypt_password

def add_test_servers():
    """添加测试服务器"""
    print("添加测试服务器...")
    
    conn = db_manager.get_connection()
    try:
        # 测试服务器列表
        test_servers = [
            {
                "name": "开发服务器",
                "host": "*************",
                "port": 22,
                "username": "root",
                "password": encrypt_password("test123"),
                "description": "开发环境测试服务器",
                "tags": json.dumps(["开发", "测试", "Docker"])
            },
            {
                "name": "测试服务器",
                "host": "*************", 
                "port": 22,
                "username": "admin",
                "password": encrypt_password("admin123"),
                "description": "功能测试服务器",
                "tags": json.dumps(["测试", "功能验证"])
            },
            {
                "name": "Docker主机",
                "host": "*************",
                "port": 22,
                "username": "docker",
                "password": encrypt_password("docker123"),
                "description": "Docker容器管理主机",
                "tags": json.dumps(["Docker", "容器", "生产"])
            }
        ]
        
        for server in test_servers:
            # 检查是否已存在同名服务器
            cursor = conn.execute("SELECT id FROM servers WHERE name = ?", (server["name"],))
            if cursor.fetchone():
                print(f"服务器 '{server['name']}' 已存在，跳过...")
                continue
                
            # 插入新服务器
            conn.execute("""
                INSERT INTO servers (name, host, port, username, password, description, tags)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                server["name"], 
                server["host"], 
                server["port"], 
                server["username"],
                server["password"], 
                server["description"], 
                server["tags"]
            ))
            
            print(f"已添加服务器: {server['name']} ({server['host']})")
        
        conn.commit()
        print("测试服务器添加完成！")
        
        # 显示当前所有服务器
        cursor = conn.execute("SELECT id, name, host, port FROM servers ORDER BY id")
        servers = cursor.fetchall()
        
        print(f"\n当前系统中的服务器:")
        print("ID\t名称\t\t\t主机地址\t\t端口")
        print("-" * 60)
        for server in servers:
            print(f"{server[0]}\t{server[1]:<15}\t{server[2]:<15}\t{server[3]}")
        
    except Exception as e:
        print(f"添加测试服务器失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    add_test_servers()
