#!/usr/bin/env python3
"""
清理测试数据脚本
删除系统中的测试数据，确保生产环境的数据整洁性
"""
import sqlite3
import os
import json
from database import db_manager

def cleanup_test_data():
    """清理测试数据"""
    print("开始清理测试数据...")
    
    conn = db_manager.get_connection()
    try:
        # 删除测试服务器数据
        test_server_names = [
            "测试服务器1", 
            "测试服务器2", 
            "Web服务器01", 
            "数据库服务器",
            "生产服务器1"
        ]
        
        # 删除测试服务器相关的监控数据
        for name in test_server_names:
            cursor = conn.execute("SELECT id FROM servers WHERE name = ?", (name,))
            server_row = cursor.fetchone()
            if server_row:
                server_id = server_row[0]
                print(f"删除服务器 '{name}' (ID: {server_id}) 的相关数据...")
                
                # 删除监控数据
                conn.execute("DELETE FROM monitoring_data WHERE server_id = ?", (server_id,))
                
                # 删除告警数据
                conn.execute("DELETE FROM alerts WHERE server_id = ?", (server_id,))
                
                # 删除脚本执行记录
                conn.execute("DELETE FROM script_executions WHERE server_id = ?", (server_id,))
                
                # 删除服务器记录
                conn.execute("DELETE FROM servers WHERE id = ?", (server_id,))
                
                print(f"已删除服务器 '{name}' 及其相关数据")
        
        # 删除测试脚本
        test_script_names = [
            "系统信息收集",
            "磁盘清理"
        ]
        
        for name in test_script_names:
            cursor = conn.execute("SELECT id FROM scripts WHERE name = ?", (name,))
            script_row = cursor.fetchone()
            if script_row:
                script_id = script_row[0]
                print(f"删除测试脚本 '{name}' (ID: {script_id})...")
                
                # 删除脚本执行记录
                conn.execute("DELETE FROM script_executions WHERE script_id = ?", (script_id,))
                
                # 删除脚本
                conn.execute("DELETE FROM scripts WHERE id = ?", (script_id,))
                
                print(f"已删除测试脚本 '{name}'")
        
        # 删除所有测试相关的标签数据
        cursor = conn.execute("SELECT id, name, tags FROM servers")
        servers = cursor.fetchall()
        
        for server in servers:
            server_id, server_name, tags_json = server
            if tags_json:
                try:
                    tags = json.loads(tags_json)
                    # 移除测试相关标签
                    test_tags = ["test", "development", "测试", "开发"]
                    cleaned_tags = [tag for tag in tags if tag not in test_tags]
                    
                    if len(cleaned_tags) != len(tags):
                        new_tags_json = json.dumps(cleaned_tags)
                        conn.execute("UPDATE servers SET tags = ? WHERE id = ?", (new_tags_json, server_id))
                        print(f"清理服务器 '{server_name}' 的测试标签")
                except json.JSONDecodeError:
                    pass
        
        # 删除测试相关的告警
        conn.execute("DELETE FROM alerts WHERE message LIKE '%测试%' OR message LIKE '%test%'")
        
        # 清理旧的监控数据（保留最近30天）
        conn.execute("""
            DELETE FROM monitoring_data 
            WHERE timestamp < datetime('now', '-30 days')
        """)
        
        # 清理旧的脚本执行记录（保留最近30天）
        conn.execute("""
            DELETE FROM script_executions 
            WHERE started_at < datetime('now', '-30 days')
        """)
        
        # 清理已解决的告警（保留最近7天）
        conn.execute("""
            DELETE FROM alerts 
            WHERE status = 'resolved' 
            AND resolved_at < datetime('now', '-7 days')
        """)
        
        conn.commit()
        print("测试数据清理完成！")
        
        # 显示清理后的统计信息
        cursor = conn.execute("SELECT COUNT(*) FROM servers")
        server_count = cursor.fetchone()[0]
        
        cursor = conn.execute("SELECT COUNT(*) FROM scripts")
        script_count = cursor.fetchone()[0]
        
        cursor = conn.execute("SELECT COUNT(*) FROM monitoring_data")
        monitoring_count = cursor.fetchone()[0]
        
        cursor = conn.execute("SELECT COUNT(*) FROM alerts")
        alert_count = cursor.fetchone()[0]
        
        print(f"\n清理后统计:")
        print(f"服务器数量: {server_count}")
        print(f"脚本数量: {script_count}")
        print(f"监控数据条数: {monitoring_count}")
        print(f"告警数量: {alert_count}")
        
    except Exception as e:
        print(f"清理测试数据失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def backup_database():
    """备份数据库"""
    import shutil
    from datetime import datetime
    
    db_path = "device_management.db"
    if os.path.exists(db_path):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"device_management_backup_{timestamp}.db"
        shutil.copy2(db_path, backup_path)
        print(f"数据库已备份到: {backup_path}")
        return backup_path
    return None

if __name__ == "__main__":
    # 备份数据库
    backup_path = backup_database()
    
    try:
        # 清理测试数据
        cleanup_test_data()
        print("\n✅ 测试数据清理成功完成！")
        
        if backup_path:
            print(f"💾 数据库备份文件: {backup_path}")
            
    except Exception as e:
        print(f"\n❌ 清理过程中出现错误: {e}")
        if backup_path:
            print(f"💾 可以从备份文件恢复: {backup_path}")
        exit(1)
